import { Button } from '@/components/ui/button'
import { ActionState } from '@/lib/types'
import { useFormStatus } from 'react-dom'

type SubmitButtonProps = {
  label: string
  loading: React.ReactNode
  actionState?: ActionState
  successLabel?: string
}

export const SubmitButton = ({ label, loading, actionState, successLabel }: SubmitButtonProps) => {
  const { pending } = useFormStatus()

  // Check if we just succeeded (prevents duplicate submissions during success callback delay)
  const justSucceeded = actionState?.status === 'SUCCESS'
  const isDisabled = pending || justSucceeded

  // Show different states based on form status
  const getButtonContent = () => {
    if (justSucceeded && successLabel) {
      return successLabel
    }
    if (pending) {
      return loading
    }
    return label
  }

  return (
    <Button disabled={isDisabled} type='submit' className='w-full'>
      {getButtonContent()}
    </Button>
  )
}
