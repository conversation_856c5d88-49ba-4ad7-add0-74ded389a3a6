{"name": "next-auth-without-next-auth", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky", "format": "prettier --write .", "postinstall": "npx prisma generate"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@node-rs/argon2": "^2.0.2", "@oslojs/crypto": "^1.0.1", "@oslojs/encoding": "^1.1.0", "@prisma/client": "^6.5.0", "@radix-ui/react-alert-dialog": "^1.1.10", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "arctic": "^3.6.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "lucide-react": "^0.487.0", "next": "15.2.4", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "8.10.1", "react-dom": "^19.1.0", "react-hook-form": "^7.55.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "use-debounce": "^10.0.4", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.0.17", "@types/node": "^22.13.14", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "eslint": "^9.23.0", "eslint-config-next": "15.2.4", "eslint-config-prettier": "^10.1.1", "husky": "^9.1.7", "lint-staged": "^15.5.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "prisma": "^6.5.0", "tailwindcss": "^4.0.17", "tw-animate-css": "^1.2.5", "typescript": "^5.8.2"}}