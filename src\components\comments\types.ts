import { Prisma } from '@prisma/client'

// Base comment type without replies to avoid circular reference
type BaseComment = Prisma.CommentGetPayload<{
  include: {
    user: { select: { username: true } }
  }
}>

// Extended comment type with replies
export type CommentWithUser = BaseComment & {
  replies?: BaseComment[]
}

export type CommentFormData = {
  content: string
  postId: string
  parentId?: string
}

export type CommentSortOption = 'newest' | 'oldest'

export type CommentSortConfig = {
  label: string
  value: CommentSortOption
  orderBy: Prisma.CommentOrderByWithRelationInput
}

export type CommentSearchParams = {
  commentSort?: CommentSortOption
  commentPage?: string
  commentLimit?: string
}
