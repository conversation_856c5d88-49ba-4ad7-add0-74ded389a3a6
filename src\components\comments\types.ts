import { Prisma } from '@prisma/client'

// Base comment type without replies to avoid circular reference
type BaseComment = Prisma.CommentGetPayload<{
  include: {
    user: { select: { username: true } }
  }
}>

// Recursive comment type with nested replies (up to 3 levels)
export type CommentWithUser = BaseComment & {
  replies?: (BaseComment & {
    replies?: (BaseComment & {
      replies?: BaseComment[]
    })[]
  })[]
}

export type CommentFormData = {
  content: string
  postId: string
  parentId?: string
}

export type CommentSortOption = 'newest' | 'oldest'

export type CommentSortConfig = {
  label: string
  value: CommentSortOption
  orderBy: Prisma.CommentOrderByWithRelationInput
}

export type CommentSearchParams = {
  commentSort?: CommentSortOption
  commentPage?: string
  commentLimit?: string
}
