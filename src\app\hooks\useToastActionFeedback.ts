import { ActionState } from '@/lib/types'
import { useEffect, useRef } from 'react'
import { toast } from 'sonner'

type Options = {
  // Custom callback to run on success and error, besides showing a toast
  onSuccessCallback?: (actionState: ActionState) => void
  onErrorCallback?: (actionState: ActionState) => void
  showToast?: boolean
  // Immediate success callback that runs synchronously to prevent race conditions
  onImmediateSuccess?: (actionState: ActionState) => void
}

/**
 * A hook that handles action state feedback with toast notifications
 * Enhanced to prevent race conditions in dialog forms
 */
const useToastActionFeedback = (actionState: ActionState, options: Options = {}) => {
  const { onSuccessCallback, onErrorCallback, showToast = true, onImmediateSuccess } = options

  const prevTimestamp = useRef(actionState.timestamp)
  const hasHandledSuccess = useRef(false)

  // Prevent the callbacks from running twice
  const isNewAction = actionState.timestamp !== prevTimestamp.current

  // Handle success case immediately to prevent duplicate submissions
  if (isNewAction && actionState.status === 'SUCCESS' && !hasHandledSuccess.current) {
    // Show toast immediately
    if (showToast && actionState.message) {
      toast.success(actionState.message)
    }

    // Run immediate success callback synchronously (for dialog closing, etc.)
    if (onImmediateSuccess) {
      onImmediateSuccess(actionState)
    }

    // Mark as handled to prevent duplicate execution
    hasHandledSuccess.current = true
    prevTimestamp.current = actionState.timestamp
  }

  // Handle success callback in useEffect for non-critical operations
  useEffect(() => {
    if (isNewAction && actionState.status === 'SUCCESS' && onSuccessCallback) {
      onSuccessCallback(actionState)
    }
  }, [actionState, onSuccessCallback, isNewAction])

  // Handle error cases in useEffect since they don't have the same timing concerns
  useEffect(() => {
    if (!isNewAction) return

    if (actionState.status === 'ERROR') {
      if (showToast && actionState.message) {
        toast.error(actionState.message)
      }
      if (onErrorCallback) {
        onErrorCallback(actionState)
      }
      prevTimestamp.current = actionState.timestamp
    }
  }, [actionState, onErrorCallback, isNewAction, showToast])

  // Reset success handler when timestamp changes (new submission)
  useEffect(() => {
    if (isNewAction && actionState.status !== 'SUCCESS') {
      hasHandledSuccess.current = false
    }
  }, [actionState.timestamp, actionState.status, isNewAction])
}

export default useToastActionFeedback
