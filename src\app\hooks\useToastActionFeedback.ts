import { ActionState } from '@/lib/types'
import { useEffect, useRef } from 'react'
import { toast } from 'sonner'

type Options = {
  // Custom callback to run on success and error, besides showing a toast
  onSuccessCallback?: (actionState: ActionState) => void
  onErrorCallback?: (actionState: ActionState) => void
  showToast?: boolean
}

/**
 * A hook that handles action state feedback with toast notifications
 */
const useToastActionFeedback = (actionState: ActionState, options: Options = {}) => {
  const { onSuccessCallback, onErrorCallback, showToast = true } = options
  const prevTimestamp = useRef(actionState.timestamp)

  // Handle success case immediately to prevent duplicate submissions
  if (actionState.timestamp !== prevTimestamp.current && actionState.status === 'SUCCESS') {
    if (showToast && actionState.message) {
      toast.success(actionState.message)
    }
    if (onSuccessCallback) {
      onSuccessCallback(actionState)
    }
    prevTimestamp.current = actionState.timestamp
  }

  // Handle error cases in useEffect since they don't have the same timing concerns
  useEffect(() => {
    if (actionState.timestamp === prevTimestamp.current) return

    if (actionState.status === 'ERROR') {
      if (showToast && actionState.message) {
        toast.error(actionState.message)
      }
      if (onErrorCallback) {
        onErrorCallback(actionState)
      }
    }

    prevTimestamp.current = actionState.timestamp
  }, [actionState, onErrorCallback, showToast])
}

export default useToastActionFeedback
