// prisma/schema.prisma

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Post {
  id         String      @id @default(cuid())
  title      String
  content    String
  status     POST_STATUS @default(DRAFT)
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt
  featuredAt DateTime?
  userId     String
  user       User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  Comment    Comment[]

  @@index([userId])
}

model User {
  id           String    @id @default(cuid())
  githubId     Int?
  username     String
  sessions     Session[]
  email        String    @unique
  passwordHash String
  posts        Post[]
  comments     Comment[]
}

// When a user signs up or signs in, we create a session for them.
// This session is stored in a database and a cookie with a session token is set in the user's browser
// Whenever the user sends a request to the server, we validate the session and if everything is fine, we return the session and the user.
model Session {
  id        String   @id
  expiresAt DateTime
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model Comment {
  id        String   @id @default(cuid())
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String?
  user      User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  postId    String
  post      Post     @relation(fields: [postId], references: [id], onDelete: Cascade)

  @@index([postId, userId])
}

enum POST_STATUS {
  DRAFT
  PUBLISHED
  PENDING
}
